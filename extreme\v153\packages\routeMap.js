// 封装统一的 routeMap 并导出
const routeMap = {
  offline: '/extreme/v153/apply/index',
  wechat: '/extreme/v153/qw/index',
  overloan: '/extreme/v153/applyOther/index',
  end: '/extreme/v153/download/index',
  halfapi: '/extreme/v153/webviewDown/index',
  wechat_official_account: '/extreme/v153/wechatOfficialAccount/index',
  user_update: '/extreme/v153/userUpdate/index',
  small_loan: '/extreme/v153/smallLoan/index',
  wechat_low: '/extreme/v153/wechatLow/index',
  wechat_link: '/extreme/v153/wechatLink/index'
}

export default routeMap;
